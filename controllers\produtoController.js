import ProdutoRepository from "../repositories/produtoRepository.js";
import Produto from "../entities/produto.js";

export default class ProdutoController {

    constructor() {
        this.produtoRepository = new ProdutoRepository();
    }

    async cadastrar(req, res) {
        try {
            const { prd_nome, prd_valor, prd_codigobarras, cat_id } = req.body;

            // Validar dados obrigatórios
            if (!prd_nome || !prd_valor || !prd_codigobarras) {
                return res.status(400).json({ message: "Nome, valor e código de barras são obrigatórios" });
            }

            // Verificar se código de barras já existe
            const codigoExiste = await this.produtoRepository.verificarCodigoBarrasExiste(prd_codigobarras);
            if (codigoExiste) {
                return res.status(409).json({ message: "Código de barras já existe" });
            }

            // Criar objeto produto
            const produto = new Produto(null, prd_nome, prd_valor, prd_codigobarras, 'S', cat_id);

            // Cadastrar produto
            const produtoId = await this.produtoRepository.cadastrar(produto);

            res.status(201).json({ id: produtoId });

        } catch (error) {
            console.error('Erro ao cadastrar produto:', error);
            res.status(500).json({ message: "Erro interno do servidor" });
        }
    }

    async inativar(req, res) {
        try {
            const { id } = req.params;

            // Verificar se produto existe
            const produto = await this.produtoRepository.obterPorId(id);
            if (!produto) {
                return res.status(404).json({ message: "Produto não encontrado" });
            }

            // Inativar produto (exclusão lógica)
            const sucesso = await this.produtoRepository.inativar(id);

            if (sucesso) {
                res.status(200).json();
            } else {
                res.status(400).json({ message: "Erro ao inativar produto" });
            }

        } catch (error) {
            console.error('Erro ao inativar produto:', error);
            res.status(500).json({ message: "Erro interno do servidor" });
        }
    }
}