import jwt from 'jsonwebtoken';
import UsuarioRepository from '../repositories/usuarioRepository.js';

const SEGREDO = 'supermercado_jwt_secret_key_2024';

export default class AuthMiddleware {

    constructor() {
        this.usuarioRepository = new UsuarioRepository();
    }

    gerarToken(id, nome, email, perfil) {
        return jwt.sign({
            id: id,
            nome: nome,
            email: email,
            perfil: perfil
        }, SEGREDO, { expiresIn: '8h' });
    }

    async validar(req, res, next) {
        const authHeader = req.headers.authorization;

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({ message: "Token não fornecido" });
        }

        const token = authHeader.substring(7);

        try {
            const decoded = jwt.verify(token, SEGREDO);

            // Validar se usuário ainda existe e está ativo
            const usuario = await this.usuarioRepository.obterPorId(decoded.id);
            if (!usuario) {
                return res.status(401).json({ message: "Usuário não encontrado" });
            }

            req.usuario = decoded;
            next();
        } catch (ex) {
            return res.status(401).json({ message: "Token inválido" });
        }
    }

    validarPerfil(perfilRequerido) {
        return (req, res, next) => {
            if (!req.usuario) {
                return res.status(401).json({ message: "Usuário não autenticado" });
            }

            if (req.usuario.perfil !== perfilRequerido && req.usuario.perfil !== 'Administrador') {
                return res.status(403).json({ message: "Acesso negado" });
            }

            next();
        };
    }
}