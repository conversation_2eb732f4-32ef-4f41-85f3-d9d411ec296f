import express from 'express';
import AuthController from '../controllers/authController.js';

const router = express.Router();
const authController = new AuthController();

// POST /auth/login - Realizar autenticação do usuário
router.post('/login', (req, res) => {
    /*
    #swagger.tags = ['Autenticação']
    #swagger.summary = 'Realizar login do usuário'
    #swagger.description = 'Realiza a autenticação do usuário através de email e senha, retornando um token JWT'
    #swagger.requestBody = {
        required: true,
        content: {
            "application/json": {
                schema: {
                    type: "object",
                    properties: {
                        email: {
                            type: "string",
                            example: "<EMAIL>"
                        },
                        senha: {
                            type: "string",
                            example: "12345"
                        }
                    },
                    required: ["email", "senha"]
                }
            }
        }
    }
    #swagger.responses[200] = {
        description: 'Login realizado com sucesso',
        content: {
            "application/json": {
                schema: {
                    type: "object",
                    properties: {
                        token: {
                            type: "string",
                            example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                        }
                    }
                }
            }
        }
    }
    #swagger.responses[400] = {
        description: 'Dados obrigatórios não fornecidos'
    }
    #swagger.responses[401] = {
        description: 'Credenciais inválidas'
    }
    #swagger.responses[500] = {
        description: 'Erro interno do servidor'
    }
    */
    authController.login(req, res);
});

export default router;