# Sistema PDV - Supermercado

Sistema de Ponto de Venda (PDV) para supermercado com gestão de produtos e operações de caixa.

## 🚀 Funcionalidades

### 1. Sistema de Autenticação JWT
- **POST /auth/login** - Realizar autenticação do usuário
- Validação por email e senha
- Geração de token JWT com perfil do usuário
- Controle de acesso baseado em perfis (Administrador e Caixa)

### 2. Gestão de Produtos (Perfil: Administrador)
- **POST /produtos** - Cadastrar novo produto
- **DELETE /produtos/:id** - Inativar produto (exclusão lógica)
- Validação de código de barras único
- Produtos inativos não aparecem nas operações de caixa

### 3. Operações de Caixa (Perfis: Administrador e Caixa)
- **POST /compras/iniciar** - Iniciar nova compra no caixa
- **POST /compras/:id/itens** - Adicionar produto à compra pelo código de barras
- **PATCH /compras/:id/finalizar** - Finalizar compra em andamento
- **GET /compras/:id/extrato** - Obter extrato completo da compra

## 🛠️ Tecnologias Utilizadas

- **Node.js** - Runtime JavaScript
- **Express.js** - Framework web
- **MySQL** - Banco de dados
- **JWT** - Autenticação
- **Swagger** - Documentação da API

## 📋 Pré-requisitos

- Node.js (versão 14 ou superior)
- MySQL
- NPM

## 🔧 Instalação

1. Clone o repositório
2. Instale as dependências:
```bash
npm install
```

3. Configure o banco de dados executando o script `db/sql-pfs2supermercado2.sql`

4. Inicie o servidor:
```bash
npm start
```

5. Acesse a documentação da API em: http://localhost:3000/docs

## 👥 Usuários Padrão

O sistema vem com dois usuários pré-cadastrados:

### Administrador
- **Email:** <EMAIL>
- **Senha:** 12345
- **Perfil:** Administrador

### Operador de Caixa
- **Email:** <EMAIL>
- **Senha:** 12345
- **Perfil:** Caixa

## 🔐 Autenticação

1. Faça login através do endpoint `/auth/login`
2. Use o token retornado no header `Authorization: Bearer {token}`
3. O token expira em 8 horas

## 📖 Documentação da API

A documentação completa da API está disponível em `/docs` quando o servidor estiver rodando.

### Endpoints Principais

#### Autenticação
- `POST /auth/login` - Login do usuário

#### Produtos (Administrador)
- `POST /produtos` - Cadastrar produto
- `DELETE /produtos/:id` - Inativar produto

#### Operações de Caixa
- `POST /compras/iniciar` - Iniciar compra
- `POST /compras/:id/itens` - Adicionar item
- `PATCH /compras/:id/finalizar` - Finalizar compra
- `GET /compras/:id/extrato` - Obter extrato

## 🏗️ Arquitetura

O sistema segue uma arquitetura em camadas:

- **Controllers** - Recebem requisições HTTP e orquestram operações
- **Repositories** - Camada de acesso aos dados
- **Entities** - Modelos de dados
- **Middlewares** - Autenticação e validação

## 🗄️ Banco de Dados

O sistema utiliza as seguintes tabelas:

- `tb_perfilmercado` - Perfis de usuário
- `tb_usuariomercado` - Usuários do sistema
- `tb_categoria` - Categorias de produtos
- `tb_produto` - Produtos
- `tb_compra` - Compras realizadas
- `tb_itenscompra` - Itens das compras

## 🔄 Fluxo de Operação

### Operação de Caixa Típica:

1. **Login** - Operador faz login no sistema
2. **Iniciar Compra** - Inicia nova compra informando CPF do cliente (opcional)
3. **Adicionar Itens** - Escaneia código de barras dos produtos
4. **Finalizar Compra** - Calcula total e finaliza a compra
5. **Extrato** - Gera extrato da compra realizada

### Gestão de Produtos (Administrador):

1. **Login** - Administrador faz login
2. **Cadastrar Produto** - Adiciona novo produto com código de barras único
3. **Inativar Produto** - Remove produto do sistema (exclusão lógica)

## 📝 Notas Importantes

- Produtos inativos não aparecem nas operações de caixa
- Códigos de barras devem ser únicos
- CPF do cliente deve ter 11 dígitos quando informado
- Compras devem ter pelo menos um item para serem finalizadas
- Tokens JWT expiram em 8 horas

## 🚀 Próximos Passos

- Implementar relatórios de vendas
- Adicionar controle de estoque
- Implementar desconto em produtos
- Adicionar formas de pagamento
