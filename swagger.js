import swaggerAutogen from "swagger-autogen";

const doc = {
    info: {
        title: "Sistema PDV - Supermercado API",
        description: "API do sistema de Ponto de Venda para supermercado da dona Maria com gestão de produtos e operações de caixa",
        version: "1.0.0"
    },
    host: 'localhost:3000',
    basePath: '/',
    schemes: ['http'],
    consumes: ['application/json'],
    produces: ['application/json'],
    tags: [
        {
            name: 'Autenticação',
            description: 'Endpoints para autenticação de usuários'
        },
        {
            name: 'Produtos',
            description: 'Gestão de produtos (apenas administradores)'
        },
        {
            name: 'Operações de Caixa',
            description: 'Operações de caixa para registro de compras'
        }
    ],
    components: {
        securitySchemes: {
            bearerAuth: {
                type: 'http',
                scheme: 'bearer',
                bearerFormat: 'JWT',
                description: 'Token JWT obtido através do endpoint de login'
            }
        }
    },
    
}

const outputJson = "./swagger-output.json";
const routes = ['./server.js']

swaggerAutogen({openapi: '3.0.0'})(outputJson, routes, doc)
.then( async () => {
    await import('./server.js');
})