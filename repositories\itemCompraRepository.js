import Database from "../db/database.js";
import ItemCompra from "../entities/itemCompra.js";

export default class ItemCompraRepository {

    constructor() {
        this.db = new Database();
    }

    async adicionarItem(compraId, produtoId, valorUnitario, quantidade) {
        let valorTotal = valorUnitario * quantidade;

        let sql = `INSERT INTO tb_itenscompra (com_id, prd_id, ico_valorunitario, ico_quantidade, ico_valortotal)
                   VALUES (?, ?, ?, ?, ?)`;

        let resultado = await this.db.ExecutaComandoLastInserted(sql, [
            compraId,
            produtoId,
            valorUnitario,
            quantidade,
            valorTotal
        ]);

        return resultado;
    }

    async verificarItemExiste(compraId, produtoId) {
        let sql = `SELECT ico_id, ico_quantidade FROM tb_itenscompra WHERE com_id = ? AND prd_id = ?`;
        let rows = await this.db.ExecutaComando(sql, [compraId, produtoId]);

        if(rows.length > 0) {
            return {
                existe: true,
                ico_id: rows[0].ico_id,
                quantidade: rows[0].ico_quantidade
            };
        }

        return { existe: false };
    }

    async atualizarQuantidade(itemId, novaQuantidade, valorUnitario) {
        let novoValorTotal = valorUnitario * novaQuantidade;

        let sql = `UPDATE tb_itenscompra SET ico_quantidade = ?, ico_valortotal = ? WHERE ico_id = ?`;
        return await this.db.ExecutaComandoNonQuery(sql, [novaQuantidade, novoValorTotal, itemId]);
    }

    async obterItensPorCompra(compraId) {
        let sql = `SELECT i.ico_id, i.com_id, i.prd_id, p.prd_nome, i.ico_valorunitario,
                          i.ico_quantidade, i.ico_valortotal
                   FROM tb_itenscompra i
                   INNER JOIN tb_produto p ON i.prd_id = p.prd_id
                   WHERE i.com_id = ?
                   ORDER BY i.ico_id`;

        let rows = await this.db.ExecutaComando(sql, [compraId]);

        let itens = [];
        for(let row of rows) {
            itens.push(new ItemCompra(row.ico_id, row.com_id, row.prd_id, row.prd_nome,
                                    row.ico_valorunitario, row.ico_quantidade, row.ico_valortotal));
        }

        return itens;
    }

    async calcularValorTotalCompra(compraId) {
        let sql = `SELECT SUM(ico_valortotal) as total FROM tb_itenscompra WHERE com_id = ?`;
        let rows = await this.db.ExecutaComando(sql, [compraId]);

        return rows[0].total || 0;
    }
}