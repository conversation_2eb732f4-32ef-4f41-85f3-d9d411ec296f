{"openapi": "3.0.0", "info": {"title": "Sistema PDV - Supermercado API", "description": "API do sistema de Ponto de Venda para supermercado da dona Maria com gestão de produtos e operações de caixa", "version": "1.0.0"}, "servers": [{"url": "http://localhost:3000/"}], "tags": [{"name": "Autenticação", "description": "Endpoints para autenticação de usuários"}, {"name": "<PERSON><PERSON><PERSON>", "description": "Gestão de produtos (apenas administradores)"}, {"name": "Operações de Caixa", "description": "Operações de caixa para registro de compras"}], "paths": {"/": {"get": {"description": "", "responses": {"200": {"description": "OK"}}}}, "/auth/login": {"post": {"tags": ["Autenticação"], "summary": "Realizar login do usuário", "description": "Realiza a autenticação do usuário através de email e senha, retornando um token JWT", "responses": {"200": {"description": "Login realizado com sucesso", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string"}}}}}}, "400": {"description": "Dados obrigatórios não fornecidos"}, "401": {"description": "Credenciais inválidas"}, "500": {"description": "Erro interno do servidor"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}, "senha": {"type": "string"}}, "required": ["email", "<PERSON><PERSON>a"]}}}}}}, "/produtos/": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Cadastrar novo produto", "description": "Cadastra um novo produto no sistema (apenas administradores)", "parameters": [{"name": "authorization", "in": "header", "schema": {"type": "string"}}], "responses": {"201": {"description": "Produto cadastrado com sucesso", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}}}}}}, "400": {"description": "Dados obrigatórios não fornecidos"}, "401": {"description": "Token não fornecido ou inválido"}, "403": {"description": "<PERSON><PERSON> negado - apenas administradores"}, "409": {"description": "Código de barras j<PERSON> existe"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"prd_nome": {"type": "string"}, "prd_valor": {"type": "number"}, "prd_codigobarras": {"type": "string"}, "cat_id": {"type": "integer"}}, "required": ["prd_nome", "prd_valor", "prd_codigobarras"]}}}}}}, "/produtos/{id}": {"delete": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Inativar produto", "description": "Realiza exclusão lógica do produto (apenas administradores)", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "ID do produto"}, {"name": "authorization", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Produto inativado com sucesso"}, "400": {"description": "Bad Request"}, "401": {"description": "Token não fornecido ou inválido"}, "403": {"description": "<PERSON><PERSON> negado - apenas administradores"}, "404": {"description": "Produto não encontrado"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}]}}, "/compras/iniciar": {"post": {"tags": ["Operações de Caixa"], "summary": "Iniciar nova compra", "description": "Inicia uma nova compra no caixa", "parameters": [{"name": "authorization", "in": "header", "schema": {"type": "string"}}], "responses": {"201": {"description": "Compra iniciada com sucesso", "content": {"application/json": {"schema": {"type": "object", "properties": {"com_id": {"type": "integer"}}}}}}, "400": {"description": "CPF inválido"}, "401": {"description": "Token não fornecido ou inválido"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"com_cpfcliente": {"type": "string", "description": "CPF do cliente (11 dígitos)"}, "com_cpfnanota": {"type": "string", "enum": ["S", "N"], "description": "indica se cliente deseja CPF na nota fiscal"}}}}}}}}, "/compras/{id}/itens": {"post": {"tags": ["Operações de Caixa"], "summary": "Adicionar item à compra", "description": "Adiciona um produto à compra pelo código de barras. Se o produto já existe, incrementa a quantidade.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "ID da compra"}, {"name": "authorization", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Item adicionado com sucesso"}, "400": {"description": "Código de barras é obrigatório"}, "401": {"description": "Token não fornecido ou inválido"}, "404": {"description": "Compra não encontrada, já finalizada ou produto não encontrado"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"prd_codigobarras": {"type": "string"}}, "required": ["prd_codigobarras"]}}}}}}, "/compras/{id}/finalizar": {"patch": {"tags": ["Operações de Caixa"], "summary": "Finalizar compra", "description": "Finaliza uma compra em andamento, calculando o valor total", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "ID da compra"}, {"name": "authorization", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Compra finalizada com sucesso"}, "400": {"description": "Compra deve ter pelo menos um item"}, "401": {"description": "Token não fornecido ou inválido"}, "404": {"description": "Compra não encontrada ou já finalizada"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}]}}, "/compras/{id}/extrato": {"get": {"tags": ["Operações de Caixa"], "summary": "Obter extrato da compra", "description": "Obtém o extrato completo de uma compra com todos os detalhes", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "ID da compra"}, {"name": "authorization", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Extrato obtido com sucesso", "content": {"application/json": {"schema": {"type": "object", "properties": {"compra": {"type": "object", "properties": {"com_id": {"type": "integer"}, "com_cpfcliente": {"type": "string"}, "com_cpfnanota": {"type": "string"}, "com_datainicio": {"type": "string"}, "com_datafim": {"type": "string"}, "com_valortotal": {"type": "number"}, "operador": {"type": "string"}}}, "itens": {"type": "array", "items": {"type": "object", "properties": {"prd_nome": {"type": "string"}, "ico_quantidade": {"type": "integer"}, "ico_valorunitario": {"type": "number"}, "ico_valortotal": {"type": "number"}}}}}}}}}, "401": {"description": "Token não fornecido ou inválido"}, "404": {"description": "Compra não encontrada"}, "500": {"description": "Erro interno do servidor"}}, "security": [{"bearerAuth": []}]}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Token JWT obtido através do endpoint de login"}}}}