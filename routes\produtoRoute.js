import express from 'express';
import ProdutoController from '../controllers/produtoController.js';
import AuthMiddleware from '../middlewares/authMiddleware.js';

const router = express.Router();
const produtoController = new ProdutoController();
const authMiddleware = new AuthMiddleware();

// POST /produtos - Cadastrar novo produto (Apenas Administrador)
router.post('/', authMiddleware.validar.bind(authMiddleware), authMiddleware.validarPerfil('Administrador'), (req, res) => {
    /*
    #swagger.tags = ['Produtos']
    #swagger.summary = 'Cadastrar novo produto'
    #swagger.description = 'Cadastra um novo produto no sistema (apenas administradores)'
    #swagger.security = [{ "bearerAuth": [] }]
    #swagger.requestBody = {
        required: true,
        content: {
            "application/json": {
                schema: {
                    type: "object",
                    properties: {
                        prd_nome: {
                            type: "string",
                            example: "Coca-Cola 2L"
                        },
                        prd_valor: {
                            type: "number",
                            example: 5.99
                        },
                        prd_codigobarras: {
                            type: "string",
                            example: "7894900011517"
                        },
                        cat_id: {
                            type: "integer",
                            example: 4
                        }
                    },
                    required: ["prd_nome", "prd_valor", "prd_codigobarras"]
                }
            }
        }
    }
    #swagger.responses[201] = {
        description: 'Produto cadastrado com sucesso',
        content: {
            "application/json": {
                schema: {
                    type: "object",
                    properties: {
                        id: {
                            type: "integer",
                            example: 1
                        }
                    }
                }
            }
        }
    }
    #swagger.responses[400] = {
        description: 'Dados obrigatórios não fornecidos'
    }
    #swagger.responses[401] = {
        description: 'Token não fornecido ou inválido'
    }
    #swagger.responses[403] = {
        description: 'Acesso negado - apenas administradores'
    }
    #swagger.responses[409] = {
        description: 'Código de barras já existe'
    }
    #swagger.responses[500] = {
        description: 'Erro interno do servidor'
    }
    */
    produtoController.cadastrar(req, res);
});

// DELETE /produtos/:id - Inativar produto (Apenas Administrador)
router.delete('/:id', authMiddleware.validar.bind(authMiddleware), authMiddleware.validarPerfil('Administrador'), (req, res) => {
    /*
    #swagger.tags = ['Produtos']
    #swagger.summary = 'Inativar produto'
    #swagger.description = 'Realiza exclusão lógica do produto (apenas administradores)'
    #swagger.security = [{ "bearerAuth": [] }]
    #swagger.parameters['id'] = {
        in: 'path',
        description: 'ID do produto',
        required: true,
        type: 'integer'
    }
    #swagger.responses[200] = {
        description: 'Produto inativado com sucesso'
    }
    #swagger.responses[401] = {
        description: 'Token não fornecido ou inválido'
    }
    #swagger.responses[403] = {
        description: 'Acesso negado - apenas administradores'
    }
    #swagger.responses[404] = {
        description: 'Produto não encontrado'
    }
    #swagger.responses[500] = {
        description: 'Erro interno do servidor'
    }
    */
    produtoController.inativar(req, res);
});

export default router;