import CompraRepository from "../repositories/compraRepository.js";
import ItemCompraRepository from "../repositories/itemCompraRepository.js";
import ProdutoRepository from "../repositories/produtoRepository.js";

export default class CompraController {

    constructor() {
        this.compraRepository = new CompraRepository();
        this.itemCompraRepository = new ItemCompraRepository();
        this.produtoRepository = new ProdutoRepository();
    }

    async iniciarCompra(req, res) {
        try {
            const { com_cpfcliente, com_cpfnanota } = req.body;
            const usuarioId = req.usuario.id;

 
            if (com_cpfcliente && com_cpfcliente.length !== 11) {
                return res.status(400).json({ message: "CPF deve ter 11 dígitos" });
            }

            // Definir flag CPF na nota (padrão 'S' se não informado)
            const cpfNaNota = com_cpfnanota || 'S';

      
            const compraId = await this.compraRepository.iniciarCompra(
                com_cpfcliente,
                cpfNaNota,
                usuarioId
            );

            res.status(201).json({ com_id: compraId });

        } catch (error) {
            console.error('Erro ao iniciar compra:', error);
            res.status(500).json({ message: "Erro interno do servidor" });
        }
    }

    async adicionarItem(req, res) {
        try {
            const { id } = req.params; // ID da compra
            const { prd_codigobarras } = req.body;

     
            if (!prd_codigobarras) {
                return res.status(400).json({ message: "Código de barras é obrigatório" });
            }

            const compraExiste = await this.compraRepository.verificarCompraExiste(id);
            if (!compraExiste) {
                return res.status(404).json({ message: "Compra não encontrada ou já finalizada" });
            }


            const produto = await this.produtoRepository.obterPorCodigoBarras(prd_codigobarras);
            if (!produto) {
                return res.status(404).json({ message: "Produto não encontrado ou inativo" });
            }

            const itemExistente = await this.itemCompraRepository.verificarItemExiste(id, produto.prd_id);

            if (itemExistente.existe) {
    
                const novaQuantidade = itemExistente.quantidade + 1;
                await this.itemCompraRepository.atualizarQuantidade(
                    itemExistente.ico_id,
                    novaQuantidade,
                    produto.prd_valor
                );
            } else {
          
                await this.itemCompraRepository.adicionarItem(
                    id,
                    produto.prd_id,
                    produto.prd_valor,
                    1
                );
            }

            res.status(200).json();

        } catch (error) {
            console.error('Erro ao adicionar item:', error);
            res.status(500).json({ message: "Erro interno do servidor" });
        }
    }

    async finalizarCompra(req, res) {
        try {
            const { id } = req.params;


            const compraExiste = await this.compraRepository.verificarCompraExiste(id);
            if (!compraExiste) {
                return res.status(404).json({ message: "Compra não encontrada ou já finalizada" });
            }

         
            const temItens = await this.compraRepository.verificarCompraTemItens(id);
            if (!temItens) {
                return res.status(400).json({ message: "Compra deve ter pelo menos um item" });
            }

        
            const valorTotal = await this.itemCompraRepository.calcularValorTotalCompra(id);

      
            const sucesso = await this.compraRepository.finalizarCompra(id, valorTotal);

            if (sucesso) {
                res.status(200).json();
            } else {
                res.status(400).json({ message: "Erro ao finalizar compra" });
            }

        } catch (error) {
            console.error('Erro ao finalizar compra:', error);
            res.status(500).json({ message: "Erro interno do servidor" });
        }
    }

    async obterExtrato(req, res) {
        try {
            const { id } = req.params;

        
            const compra = await this.compraRepository.obterPorId(id);
            if (!compra) {
                return res.status(404).json({ message: "Compra não encontrada" });
            }

    
            const itens = await this.itemCompraRepository.obterItensPorCompra(id);

            // Montar extrato
            const extrato = {
                compra: {
                    com_id: compra.com_id,
                    com_cpfcliente: compra.com_cpfcliente,
                    com_cpfnanota: compra.com_cpfnanota,
                    com_datainicio: compra.com_datainicio,
                    com_datafim: compra.com_datafim,
                    com_valortotal: compra.com_valortotal,
                    operador: compra.usu_nome
                },
                itens: itens.map(item => ({
                    prd_nome: item.prd_nome,
                    ico_quantidade: item.ico_quantidade,
                    ico_valorunitario: item.ico_valorunitario,
                    ico_valortotal: item.ico_valortotal
                }))
            };

            res.status(200).json(extrato);

        } catch (error) {
            console.error('Erro ao obter extrato:', error);
            res.status(500).json({ message: "Erro interno do servidor" });
        }
    }
}