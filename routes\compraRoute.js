import express from 'express';
import CompraController from '../controllers/compraController.js';
import AuthMiddleware from '../middlewares/authMiddleware.js';

const router = express.Router();
const compraController = new CompraController();
const authMiddleware = new AuthMiddleware();

// POST /compras/iniciar - Iniciar nova compra no caixa
router.post('/iniciar', authMiddleware.validar.bind(authMiddleware), (req, res) => {
    /*
    #swagger.tags = ['Operações de Caixa']
    #swagger.summary = 'Iniciar nova compra'
    #swagger.description = 'Inicia uma nova compra no caixa'
    #swagger.security = [{ "bearerAuth": [] }]
    #swagger.requestBody = {
        required: false,
        content: {
            "application/json": {
                schema: {
                    type: "object",
                    properties: {
                        com_cpfcliente: {
                            type: "string",
                            example: "12345678901",
                            description: "CPF do cliente (11 dígitos)"
                        },
                        com_cpfnanota: {
                            type: "string",
                            enum: ["S", "N"],
                            example: "S",
                            description: "Flag indicando se cliente deseja CPF na nota fiscal"
                        }
                    }
                }
            }
        }
    }
    #swagger.responses[201] = {
        description: 'Compra iniciada com sucesso',
        content: {
            "application/json": {
                schema: {
                    type: "object",
                    properties: {
                        com_id: {
                            type: "integer",
                            example: 1
                        }
                    }
                }
            }
        }
    }
    #swagger.responses[400] = {
        description: 'CPF inválido'
    }
    #swagger.responses[401] = {
        description: 'Token não fornecido ou inválido'
    }
    #swagger.responses[500] = {
        description: 'Erro interno do servidor'
    }
    */
    compraController.iniciarCompra(req, res);
});

// POST /compras/:id/itens - Adicionar produto à compra pelo código de barras
router.post('/:id/itens', authMiddleware.validar.bind(authMiddleware), (req, res) => {
    /*
    #swagger.tags = ['Operações de Caixa']
    #swagger.summary = 'Adicionar item à compra'
    #swagger.description = 'Adiciona um produto à compra pelo código de barras. Se o produto já existe, incrementa a quantidade.'
    #swagger.security = [{ "bearerAuth": [] }]
    #swagger.parameters['id'] = {
        in: 'path',
        description: 'ID da compra',
        required: true,
        type: 'integer'
    }
    #swagger.requestBody = {
        required: true,
        content: {
            "application/json": {
                schema: {
                    type: "object",
                    properties: {
                        prd_codigobarras: {
                            type: "string",
                            example: "5879628483562"
                        }
                    },
                    required: ["prd_codigobarras"]
                }
            }
        }
    }
    #swagger.responses[200] = {
        description: 'Item adicionado com sucesso'
    }
    #swagger.responses[400] = {
        description: 'Código de barras é obrigatório'
    }
    #swagger.responses[401] = {
        description: 'Token não fornecido ou inválido'
    }
    #swagger.responses[404] = {
        description: 'Compra não encontrada, já finalizada ou produto não encontrado'
    }
    #swagger.responses[500] = {
        description: 'Erro interno do servidor'
    }
    */
    compraController.adicionarItem(req, res);
});

// PATCH /compras/:id/finalizar - Finalizar compra em andamento
router.patch('/:id/finalizar', authMiddleware.validar.bind(authMiddleware), (req, res) => {
    /*
    #swagger.tags = ['Operações de Caixa']
    #swagger.summary = 'Finalizar compra'
    #swagger.description = 'Finaliza uma compra em andamento, calculando o valor total'
    #swagger.security = [{ "bearerAuth": [] }]
    #swagger.parameters['id'] = {
        in: 'path',
        description: 'ID da compra',
        required: true,
        type: 'integer'
    }
    #swagger.responses[200] = {
        description: 'Compra finalizada com sucesso'
    }
    #swagger.responses[400] = {
        description: 'Compra deve ter pelo menos um item'
    }
    #swagger.responses[401] = {
        description: 'Token não fornecido ou inválido'
    }
    #swagger.responses[404] = {
        description: 'Compra não encontrada ou já finalizada'
    }
    #swagger.responses[500] = {
        description: 'Erro interno do servidor'
    }
    */
    compraController.finalizarCompra(req, res);
});

// GET /compras/:id/extrato - Obter extrato completo da compra
router.get('/:id/extrato', authMiddleware.validar.bind(authMiddleware), (req, res) => {
    /*
    #swagger.tags = ['Operações de Caixa']
    #swagger.summary = 'Obter extrato da compra'
    #swagger.description = 'Obtém o extrato completo de uma compra com todos os detalhes'
    #swagger.security = [{ "bearerAuth": [] }]
    #swagger.parameters['id'] = {
        in: 'path',
        description: 'ID da compra',
        required: true,
        type: 'integer'
    }
    #swagger.responses[200] = {
        description: 'Extrato obtido com sucesso',
        content: {
            "application/json": {
                schema: {
                    type: "object",
                    properties: {
                        compra: {
                            type: "object",
                            properties: {
                                com_id: { type: "integer", example: 1 },
                                com_cpfcliente: { type: "string", example: "12345678901" },
                                com_cpfnanota: { type: "string", example: "S" },
                                com_datainicio: { type: "string", example: "2024-01-15T10:30:00.000Z" },
                                com_datafim: { type: "string", example: "2024-01-15T10:45:00.000Z" },
                                com_valortotal: { type: "number", example: 15.50 },
                                operador: { type: "string", example: "Fulano" }
                            }
                        },
                        itens: {
                            type: "array",
                            items: {
                                type: "object",
                                properties: {
                                    prd_nome: { type: "string", example: "Bavaria Latão" },
                                    ico_quantidade: { type: "integer", example: 2 },
                                    ico_valorunitario: { type: "number", example: 2.90 },
                                    ico_valortotal: { type: "number", example: 5.80 }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    #swagger.responses[401] = {
        description: 'Token não fornecido ou inválido'
    }
    #swagger.responses[404] = {
        description: 'Compra não encontrada'
    }
    #swagger.responses[500] = {
        description: 'Erro interno do servidor'
    }
    */
    compraController.obterExtrato(req, res);
});

export default router;