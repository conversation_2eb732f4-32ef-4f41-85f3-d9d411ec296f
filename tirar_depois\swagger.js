import swaggerAutogen from "swagger-autogen";
const doc = {
    info: {
        title: "Sistema de Gerenciamento de RH - API",
        description: "API completa para gerenciamento do departamento de Recursos Humanos, incluindo cadastro de funcionários, aplicação de aumentos salariais e geração de folhas de pagamento.",
        version: "1.0.0",
        contact: {
            name: "DevMinds Team",
            email: "<EMAIL>"
        }
    },
    host: 'localhost:5000',
    basePath: '/',
    schemes: ['http'],
    consumes: ['application/json'],
    produces: ['application/json'],
    tags: [
        {
            name: 'Autenticação',
            description: 'Endpoints para autenticação de usuários'
        },
        {
            name: 'Funcion<PERSON><PERSON>s',
            description: 'Gerenciamento de funcionários'
        },
        {
            name: 'Aumento Salarial',
            description: 'Aplicação e histórico de aumentos salariais'
        },
        {
            name: 'Cargos',
            description: 'Gerenciamento de cargos'
        },
        {
            name: '<PERSON><PERSON><PERSON>',
            description: 'Geração e consulta de folhas de pagamento'
        }
    ],
    components: {
        securitySchemes: {
            bearerAuth: {
                type: 'http',
                scheme: 'bearer',
                bearerFormat: 'JWT',
                description: 'Token JWT obtido através do endpoint de autenticação'
            }
        },

    },
    security: [
        {
            bearerAuth: []
        }
    ],
    paths: {
        '/auth/login': {
            post: {
                tags: ['Autenticação'],
                summary: 'Autenticar usuário',
                description: 'Realiza a autenticação do usuário e retorna um token JWT para acesso aos endpoints protegidos.',
                security: [],
                requestBody: {
                    required: true,
                    content: {
                        'application/json': {
                            schema: {
                                $ref: '#/components/schemas/LoginRequest'
                            }
                        }
                    }
                },
                responses: {
                    200: {
                        description: 'Autenticação realizada com sucesso',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/LoginResponse'
                                }
                            }
                        }
                    },
                    401: {
                        description: 'Credenciais inválidas',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                },
                                example: {
                                    msg: 'Usuário ou senha inválidos!'
                                }
                            }
                        }
                    },
                    500: {
                        description: 'Erro interno do servidor',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    }
                }
            }
        },
        '/funcionarios': {
            get: {
                tags: ['Funcionários'],
                summary: 'Listar funcionários',
                description: 'Retorna a lista de todos os funcionários cadastrados no sistema.',
                security: [{ bearerAuth: [] }],
                responses: {
                    200: {
                        description: 'Lista de funcionários retornada com sucesso',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'array',
                                    items: {
                                        $ref: '#/components/schemas/Funcionario'
                                    }
                                }
                            }
                        }
                    },
                    401: {
                        description: 'Token de autenticação inválido ou ausente',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    },
                    500: {
                        description: 'Erro interno do servidor',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    }
                }
            },
            post: {
                tags: ['Funcionários'],
                summary: 'Cadastrar funcionário',
                description: 'Cadastra um novo funcionário no sistema.',
                security: [{ bearerAuth: [] }],
                requestBody: {
                    required: true,
                    content: {
                        'application/json': {
                            schema: {
                                $ref: '#/components/schemas/FuncionarioRequest'
                            }
                        }
                    }
                },
                responses: {
                    201: {
                        description: 'Funcionário cadastrado com sucesso',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/SuccessResponse'
                                },
                                example: {
                                    msg: 'Funcionário cadastrado com sucesso!'
                                }
                            }
                        }
                    },
                    400: {
                        description: 'Dados inválidos ou obrigatórios ausentes',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                },
                                example: {
                                    msg: 'CPF, nome, salário e cargo são obrigatórios!'
                                }
                            }
                        }
                    },
                    401: {
                        description: 'Token de autenticação inválido ou ausente',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    },
                    500: {
                        description: 'Erro interno do servidor',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    }
                }
            }
        },
        '/funcionarios/{id}': {
            get: {
                tags: ['Funcionários'],
                summary: 'Buscar funcionário por ID',
                description: 'Retorna os dados de um funcionário específico pelo seu ID.',
                security: [{ bearerAuth: [] }],
                parameters: [
                    {
                        name: 'id',
                        in: 'path',
                        required: true,
                        description: 'ID do funcionário',
                        schema: {
                            type: 'integer',
                            example: 1
                        }
                    }
                ],
                responses: {
                    200: {
                        description: 'Funcionário encontrado',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Funcionario'
                                }
                            }
                        }
                    },
                    400: {
                        description: 'ID inválido',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                },
                                example: {
                                    msg: 'ID do funcionário é obrigatório!'
                                }
                            }
                        }
                    },
                    404: {
                        description: 'Funcionário não encontrado',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                },
                                example: {
                                    msg: 'Funcionário não encontrado!'
                                }
                            }
                        }
                    },
                    401: {
                        description: 'Token de autenticação inválido ou ausente',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    },
                    500: {
                        description: 'Erro interno do servidor',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    }
                }
            },
            delete: {
                tags: ['Funcionários'],
                summary: 'Demitir funcionário',
                description: 'Realiza a demissão de um funcionário, definindo a data de demissão.',
                security: [{ bearerAuth: [] }],
                parameters: [
                    {
                        name: 'id',
                        in: 'path',
                        required: true,
                        description: 'ID do funcionário',
                        schema: {
                            type: 'integer',
                            example: 1
                        }
                    }
                ],
                responses: {
                    200: {
                        description: 'Funcionário demitido com sucesso',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/SuccessResponse'
                                },
                                example: {
                                    msg: 'Funcionário demitido com sucesso!'
                                }
                            }
                        }
                    },
                    400: {
                        description: 'ID inválido',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                },
                                example: {
                                    msg: 'ID do funcionário é obrigatório!'
                                }
                            }
                        }
                    },
                    404: {
                        description: 'Funcionário não encontrado',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                },
                                example: {
                                    msg: 'Funcionário não encontrado!'
                                }
                            }
                        }
                    },
                    401: {
                        description: 'Token de autenticação inválido ou ausente',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    },
                    500: {
                        description: 'Erro interno do servidor',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    }
                }
            }
        },
        '/cargos': {
            get: {
                tags: ['Cargos'],
                summary: 'Listar cargos',
                description: 'Retorna a lista de todos os cargos disponíveis no sistema.',
                security: [{ bearerAuth: [] }],
                responses: {
                    200: {
                        description: 'Lista de cargos retornada com sucesso',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'array',
                                    items: {
                                        $ref: '#/components/schemas/Cargo'
                                    }
                                }
                            }
                        }
                    },
                    401: {
                        description: 'Token de autenticação inválido ou ausente',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    },
                    500: {
                        description: 'Erro interno do servidor',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    }
                }
            }
        },
        '/aumentoSalario/aplicar': {
            post: {
                tags: ['Aumento Salarial'],
                summary: 'Aplicar aumento salarial',
                description: 'Aplica um aumento salarial percentual a todos os funcionários ativos e registra no histórico.',
                security: [{ bearerAuth: [] }],
                requestBody: {
                    required: true,
                    content: {
                        'application/json': {
                            schema: {
                                $ref: '#/components/schemas/AumentoSalarioRequest'
                            }
                        }
                    }
                },
                responses: {
                    200: {
                        description: 'Aumento salarial aplicado com sucesso',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'object',
                                    properties: {
                                        msg: {
                                            type: 'string',
                                            example: 'Aumento salarial aplicado com sucesso!'
                                        },
                                        percentual: {
                                            type: 'number',
                                            format: 'decimal',
                                            example: 10.5
                                        },
                                        data: {
                                            type: 'string',
                                            format: 'date',
                                            example: '2024-03-15'
                                        },
                                        usuario: {
                                            type: 'string',
                                            example: 'Admin RH'
                                        }
                                    }
                                }
                            }
                        }
                    },
                    400: {
                        description: 'Percentual inválido',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                },
                                example: {
                                    msg: 'Percentual é obrigatório e deve ser maior que 0!'
                                }
                            }
                        }
                    },
                    401: {
                        description: 'Token de autenticação inválido ou ausente',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    },
                    500: {
                        description: 'Erro interno do servidor',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    }
                }
            }
        },
        '/aumentoSalario/historico': {
            get: {
                tags: ['Aumento Salarial'],
                summary: 'Listar histórico de aumentos',
                description: 'Retorna o histórico de todos os aumentos salariais aplicados.',
                security: [{ bearerAuth: [] }],
                responses: {
                    200: {
                        description: 'Histórico de aumentos retornado com sucesso',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'array',
                                    items: {
                                        $ref: '#/components/schemas/AumentoSalario'
                                    }
                                }
                            }
                        }
                    },
                    401: {
                        description: 'Token de autenticação inválido ou ausente',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    },
                    500: {
                        description: 'Erro interno do servidor',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    }
                }
            }
        },
        '/folhaPagamento/gerar': {
            post: {
                tags: ['Folha de Pagamento'],
                summary: 'Gerar folha de pagamento',
                description: 'Gera uma nova folha de pagamento para o mês e ano especificados, incluindo todos os funcionários ativos.',
                security: [{ bearerAuth: [] }],
                requestBody: {
                    required: true,
                    content: {
                        'application/json': {
                            schema: {
                                $ref: '#/components/schemas/FolhaPagamentoRequest'
                            }
                        }
                    }
                },
                responses: {
                    200: {
                        description: 'Folha de pagamento gerada com sucesso',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/SuccessResponse'
                                },
                                example: {
                                    msg: 'Folha gerada com sucesso!'
                                }
                            }
                        }
                    },
                    400: {
                        description: 'Dados obrigatórios ausentes',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                },
                                example: {
                                    msg: 'Mês e ano são obrigatórios!'
                                }
                            }
                        }
                    },
                    401: {
                        description: 'Token de autenticação inválido ou ausente',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    },
                    500: {
                        description: 'Erro interno do servidor',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    }
                }
            }
        },
        '/folhaPagamento': {
            get: {
                tags: ['Folha de Pagamento'],
                summary: 'Listar folhas de pagamento',
                description: 'Retorna a lista de todas as folhas de pagamento geradas.',
                security: [{ bearerAuth: [] }],
                responses: {
                    200: {
                        description: 'Lista de folhas de pagamento retornada com sucesso',
                        content: {
                            'application/json': {
                                schema: {
                                    type: 'array',
                                    items: {
                                        $ref: '#/components/schemas/FolhaPagamento'
                                    }
                                }
                            }
                        }
                    },
                    401: {
                        description: 'Token de autenticação inválido ou ausente',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    },
                    500: {
                        description: 'Erro interno do servidor',
                        content: {
                            'application/json': {
                                schema: {
                                    $ref: '#/components/schemas/Error'
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

const outputJson = "./swagger-output.json";
const routes = ['./server.js']

swaggerAutogen({openapi: '3.0.0'})(outputJson, routes, doc)
.then( async () => {
    await import('./server.js');
})