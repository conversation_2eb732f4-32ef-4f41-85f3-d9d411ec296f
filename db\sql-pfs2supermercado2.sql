
CREATE TABLE tb_perfilmercado (
  per_id int NOT NULL AUTO_INCREMENT,
  per_descricao varchar(100) DEFAULT NULL,
  PRIMARY KEY (per_id)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE tb_usuariomercado (
  usu_id int NOT NULL AUTO_INCREMENT,
  usu_nome varchar(200) DEFAULT NULL,
  usu_email varchar(100) DEFAULT NULL,
  usu_ativo enum('S', 'N') DEFAULT 'S',
  usu_senha varchar(100) DEFAULT NULL,
  per_id int DEFAULT NULL,
  PRIMARY KEY (usu_id),
  KEY fk_usuario_perfil (per_id),
  CONSTRAINT fk_usuariomercado_perfil FOREIGN KEY (per_id) REFERENCES tb_perfilmercado (per_id)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

create table tb_categoria (
	cat_id int primary key auto_increment,
    cat_descricao varchar(100) NOT NULL
);

create table tb_produto (
	prd_id int primary key auto_increment,
	prd_nome varchar(200) NOT NULL,
    prd_valor decimal(10,2) NOT NULL,
	prd_codigobarras varchar(100) UNIQUE NOT NULL,
    prd_ativo enum('S', 'N') DEFAULT 'S',
    cat_id int,
    
    constraint fk_produto_categoria foreign key (cat_id) references tb_categoria (cat_id)
);

create table tb_compra (
	com_id int primary key auto_increment,
    com_cpfcliente varchar(15),
    com_cpfnanota enum('S', 'N') DEFAULT 'S',
    com_datainicio datetime,
    com_datafim datetime,
    com_valortotal decimal(10,2),
    usu_id int,
    
    constraint fk_usuario_compra foreign key (usu_id) references tb_usuariomercado (usu_id)
);

create table tb_itenscompra (
	ico_id int primary key auto_increment,
    com_id int,
    prd_id int,
    ico_valorunitario decimal(10,2),
    ico_quantidade int,
    ico_valortotal decimal(10,2),
    
    constraint fk_itemcompra_compra foreign key (com_id) references tb_compra (com_id),
    constraint fk_itemcompra_produto foreign key (prd_id) references tb_produto (prd_id)
);

insert into tb_perfilmercado (per_descricao) values ('Administrador');
insert into tb_perfilmercado (per_descricao) values ('Caixa');

insert into tb_categoria (cat_descricao) values ('Frios e laticínios');
insert into tb_categoria (cat_descricao) values ('Padaria');
insert into tb_categoria (cat_descricao) values ('Açougue');
insert into tb_categoria (cat_descricao) values ('Adega e bebidas');
insert into tb_categoria (cat_descricao) values ('Higiene e limpeza');
insert into tb_categoria (cat_descricao) values ('Hortifruti e mercearia');
insert into tb_categoria (cat_descricao) values ('Enlatados');
insert into tb_categoria (cat_descricao) values ('Cereais');

insert into tb_usuariomercado (usu_nome, usu_email, usu_ativo, usu_senha, per_id)
values ('Fulano', '<EMAIL>', 'S', '12345', 1);
insert into tb_usuariomercado (usu_nome, usu_email, usu_ativo, usu_senha, per_id)
values ('Ciclano', '<EMAIL>', 'S', '12345', 2);



insert into tb_produto (prd_nome, prd_valor, prd_codigobarras, prd_ativo, cat_id) 
values ("Bavaria Latão", 2.90, "5879628483562", "S", 4);