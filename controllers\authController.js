import UsuarioRepository from "../repositories/usuarioRepository.js";
import AuthMiddleware from "../middlewares/authMiddleware.js";

export default class AuthController {

    constructor() {
        this.usuarioRepository = new UsuarioRepository();
        this.authMiddleware = new AuthMiddleware();
    }

    async login(req, res) {
        try {
            const { email, senha } = req.body;

           
            if (!email || !senha) {
                return res.status(400).json({ message: "Email e senha são obrigatórios" });
            }

       
            const usuario = await this.usuarioRepository.obterPorEmailSenha(email, senha);

            if (!usuario) {
                return res.status(401).json({ message: "Credenciais inválidas" });
            }

   
            const token = this.authMiddleware.gerarToken(
                usuario.usu_id,
                usuario.usu_nome,
                usuario.usu_email,
                usuario.per_descricao
            );

            res.status(200).json({ token });

        } catch (error) {
            console.error('Erro no login:', error);
            res.status(500).json({ message: "Erro interno do servidor" });
        }
    }
}